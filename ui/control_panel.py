"""
控制面板UI - 录制和播放控制界面
"""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                             QPushButton, QLabel, QSlider, QProgressBar,
                             QSpinBox, QDoubleSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette


class ControlPanel(QWidget):
    """控制面板类"""

    # 信号定义
    record_started = pyqtSignal()
    record_paused = pyqtSignal()
    record_stopped = pyqtSignal()
    playback_started = pyqtSignal()
    playback_paused = pyqtSignal()
    playback_stopped = pyqtSignal()
    speed_changed = pyqtSignal(float)
    open_file_requested = pyqtSignal()  # 新增：打开文件信号
    save_file_requested = pyqtSignal()  # 新增：保存文件信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_recording = False
        self.is_playing = False
        self.is_record_paused = False
        self.is_play_paused = False

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 文件操作组
        self.create_file_group(layout)

        # 录制控制组
        self.create_record_group(layout)

        # 播放控制组
        self.create_playback_group(layout)

        # 设置组
        self.create_settings_group(layout)

        # 状态显示组
        self.create_status_group(layout)

        # 添加弹性空间
        layout.addStretch()

    def create_file_group(self, parent_layout):
        """创建文件操作组"""
        group = QGroupBox("文件操作")
        layout = QVBoxLayout(group)

        # 文件操作按钮行
        button_layout = QHBoxLayout()

        # 打开文件按钮
        self.open_file_btn = QPushButton("📂 打开录制")
        self.open_file_btn.setMinimumHeight(35)
        self.open_file_btn.setToolTip("打开已保存的录制文件 (Ctrl+O)")
        self.open_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)

        # 保存文件按钮
        self.save_file_btn = QPushButton("💾 保存录制")
        self.save_file_btn.setMinimumHeight(35)
        self.save_file_btn.setToolTip("保存当前录制 (Ctrl+S)")
        self.save_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)

        button_layout.addWidget(self.open_file_btn)
        button_layout.addWidget(self.save_file_btn)
        layout.addLayout(button_layout)

        parent_layout.addWidget(group)

    def create_record_group(self, parent_layout):
        """创建录制控制组"""
        group = QGroupBox("录制控制")
        layout = QVBoxLayout(group)

        # 录制按钮行
        button_layout = QHBoxLayout()

        self.record_btn = QPushButton("开始录制")
        self.record_btn.setMinimumHeight(40)
        self.record_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

        self.pause_record_btn = QPushButton("暂停")
        self.pause_record_btn.setMinimumHeight(40)
        self.pause_record_btn.setEnabled(False)

        self.stop_record_btn = QPushButton("停止")
        self.stop_record_btn.setMinimumHeight(40)
        self.stop_record_btn.setEnabled(False)
        self.stop_record_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c1170a;
            }
        """)

        button_layout.addWidget(self.record_btn)
        button_layout.addWidget(self.pause_record_btn)
        button_layout.addWidget(self.stop_record_btn)

        layout.addLayout(button_layout)

        # 录制状态显示
        self.record_status_label = QLabel("状态: 就绪")
        self.record_count_label = QLabel("操作数: 0")
        self.record_time_label = QLabel("时长: 0.0s")

        layout.addWidget(self.record_status_label)
        layout.addWidget(self.record_count_label)
        layout.addWidget(self.record_time_label)

        parent_layout.addWidget(group)

    def create_playback_group(self, parent_layout):
        """创建播放控制组"""
        group = QGroupBox("播放控制")
        layout = QVBoxLayout(group)

        # 播放按钮行
        button_layout = QHBoxLayout()

        self.play_btn = QPushButton("开始播放")
        self.play_btn.setMinimumHeight(40)
        self.play_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)

        self.pause_play_btn = QPushButton("暂停")
        self.pause_play_btn.setMinimumHeight(40)
        self.pause_play_btn.setEnabled(False)

        self.stop_play_btn = QPushButton("停止")
        self.stop_play_btn.setMinimumHeight(40)
        self.stop_play_btn.setEnabled(False)

        button_layout.addWidget(self.play_btn)
        button_layout.addWidget(self.pause_play_btn)
        button_layout.addWidget(self.stop_play_btn)

        layout.addLayout(button_layout)

        # 播放进度
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        layout.addWidget(QLabel("播放进度:"))
        layout.addWidget(self.progress_bar)

        # 播放速度控制
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("播放速度:"))

        self.speed_spinbox = QDoubleSpinBox()
        self.speed_spinbox.setMinimum(0.1)
        self.speed_spinbox.setMaximum(10.0)
        self.speed_spinbox.setSingleStep(0.1)
        self.speed_spinbox.setValue(1.0)
        self.speed_spinbox.setSuffix("x")

        speed_layout.addWidget(self.speed_spinbox)
        speed_layout.addStretch()

        layout.addLayout(speed_layout)

        parent_layout.addWidget(group)

    def create_settings_group(self, parent_layout):
        """创建设置组"""
        group = QGroupBox("录制设置")
        layout = QVBoxLayout(group)

        # 录制鼠标移动
        self.record_mouse_move_cb = QCheckBox("录制鼠标移动")
        self.record_mouse_move_cb.setChecked(True)
        layout.addWidget(self.record_mouse_move_cb)

        # 自动保存
        self.auto_save_cb = QCheckBox("录制完成后自动保存")
        self.auto_save_cb.setChecked(True)
        layout.addWidget(self.auto_save_cb)

        parent_layout.addWidget(group)

    def create_status_group(self, parent_layout):
        """创建状态显示组"""
        group = QGroupBox("系统状态")
        layout = QVBoxLayout(group)

        self.system_status_label = QLabel("系统: 正常")
        self.memory_usage_label = QLabel("内存使用: 0 MB")

        layout.addWidget(self.system_status_label)
        layout.addWidget(self.memory_usage_label)

        parent_layout.addWidget(group)

    def setup_connections(self):
        """设置信号连接"""
        # 文件操作按钮
        self.open_file_btn.clicked.connect(self.on_open_file_clicked)
        self.save_file_btn.clicked.connect(self.on_save_file_clicked)

        # 录制按钮
        self.record_btn.clicked.connect(self.on_record_clicked)
        self.pause_record_btn.clicked.connect(self.on_pause_record_clicked)
        self.stop_record_btn.clicked.connect(self.on_stop_record_clicked)

        # 播放按钮
        self.play_btn.clicked.connect(self.on_play_clicked)
        self.pause_play_btn.clicked.connect(self.on_pause_play_clicked)
        self.stop_play_btn.clicked.connect(self.on_stop_play_clicked)

        # 播放速度
        self.speed_spinbox.valueChanged.connect(self.on_speed_changed)

    def on_record_clicked(self):
        """录制按钮点击"""
        if not self.is_recording:
            self.record_started.emit()

    def on_pause_record_clicked(self):
        """暂停录制按钮点击"""
        if self.is_recording:
            if self.is_record_paused:
                self.record_started.emit()  # 恢复录制
            else:
                self.record_paused.emit()

    def on_stop_record_clicked(self):
        """停止录制按钮点击"""
        if self.is_recording:
            self.record_stopped.emit()

    def on_play_clicked(self):
        """播放按钮点击"""
        if not self.is_playing:
            self.playback_started.emit()

    def on_pause_play_clicked(self):
        """暂停播放按钮点击"""
        if self.is_playing:
            if self.is_play_paused:
                self.playback_started.emit()  # 恢复播放
            else:
                self.playback_paused.emit()

    def on_stop_play_clicked(self):
        """停止播放按钮点击"""
        if self.is_playing:
            self.playback_stopped.emit()

    def on_speed_changed(self, value):
        """播放速度改变"""
        self.speed_changed.emit(value)

    def set_recording_state(self, recording):
        """设置录制状态"""
        self.is_recording = recording

        if recording:
            self.record_btn.setText("录制中...")
            self.record_btn.setEnabled(False)
            self.pause_record_btn.setEnabled(True)
            self.stop_record_btn.setEnabled(True)
            self.record_status_label.setText("状态: 录制中")
        else:
            self.record_btn.setText("开始录制")
            self.record_btn.setEnabled(True)
            self.pause_record_btn.setEnabled(False)
            self.stop_record_btn.setEnabled(False)
            self.record_status_label.setText("状态: 就绪")
            self.is_record_paused = False

    def set_playing_state(self, playing):
        """设置播放状态"""
        self.is_playing = playing

        if playing:
            self.play_btn.setText("播放中...")
            self.play_btn.setEnabled(False)
            self.pause_play_btn.setEnabled(True)
            self.stop_play_btn.setEnabled(True)
        else:
            self.play_btn.setText("开始播放")
            self.play_btn.setEnabled(True)
            self.pause_play_btn.setEnabled(False)
            self.stop_play_btn.setEnabled(False)
            self.is_play_paused = False
            self.progress_bar.setValue(0)

    def update_record_status(self, status):
        """更新录制状态显示"""
        self.record_count_label.setText(f"操作数: {status['action_count']}")
        self.record_time_label.setText(f"时长: {status['duration']:.1f}s")

    def update_playback_status(self, status):
        """更新播放状态显示"""
        progress = int(status['progress'] * 100)
        self.progress_bar.setValue(progress)

    def update_progress(self, progress):
        """更新播放进度"""
        self.progress_bar.setValue(int(progress * 100))

    # 文件操作事件处理
    def on_open_file_clicked(self):
        """打开文件按钮点击事件"""
        self.open_file_requested.emit()

    def on_save_file_clicked(self):
        """保存文件按钮点击事件"""
        self.save_file_requested.emit()