"""
主窗口UI - 应用程序的主界面
"""
import sys
import os
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QSplitter, QMenuBar, QMenu, QAction,
                             QStatusBar, QMessageBox, QFileDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QKeySequence

from .control_panel import ControlPanel
from .action_list import ActionListWidget
from .settings_dialog import SettingsDialog
from core.recorder import ActionRecorder
from core.player import ActionPlayer
from core.data_manager import DataManager


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.recorder = ActionRecorder()
        self.player = ActionPlayer()
        self.data_manager = DataManager()

        self.current_recording_file = None
        self.recent_files = []  # 最近打开的文件列表
        self.max_recent_files = 5  # 最多保存5个最近文件
        self.settings = {
            'auto_save': True,
            'play_speed': 1.0,
            'record_mouse_move': True,
            'hotkey_record': 'F9',
            'hotkey_play': 'F10',
            'hotkey_stop': 'F11'
        }

        self.init_ui()
        self.setup_connections()
        self.setup_timer()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("操作录制器 - Operation Recorder")
        self.setGeometry(100, 100, 1000, 700)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 创建控制面板
        self.control_panel = ControlPanel()
        splitter.addWidget(self.control_panel)

        # 创建操作列表
        self.action_list = ActionListWidget()
        splitter.addWidget(self.action_list)

        # 设置分割器比例
        splitter.setSizes([300, 700])

        # 创建菜单栏
        self.create_menu_bar()

        # 创建状态栏
        self.create_status_bar()

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 新建录制
        new_action = QAction('新建录制(&N)', self)
        new_action.setShortcut(QKeySequence.New)
        new_action.triggered.connect(self.new_recording)
        file_menu.addAction(new_action)

        # 打开录制
        open_action = QAction('打开录制(&O)', self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.triggered.connect(self.open_recording)
        file_menu.addAction(open_action)

        # 最近打开的文件
        self.recent_files_menu = file_menu.addMenu('最近打开(&R)')
        self.update_recent_files_menu()

        # 保存录制
        save_action = QAction('保存录制(&S)', self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.triggered.connect(self.save_recording)
        file_menu.addAction(save_action)

        # 另存为
        save_as_action = QAction('另存为(&A)', self)
        save_as_action.setShortcut(QKeySequence.SaveAs)
        save_as_action.triggered.connect(self.save_recording_as)
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        # 导入
        import_action = QAction('导入录制(&I)', self)
        import_action.triggered.connect(self.import_recording)
        file_menu.addAction(import_action)

        # 导出
        export_action = QAction('导出录制(&E)', self)
        export_action.triggered.connect(self.export_recording)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 录制菜单
        record_menu = menubar.addMenu('录制(&R)')

        # 开始录制
        start_record_action = QAction('开始录制(&S)', self)
        start_record_action.setShortcut('F9')
        start_record_action.triggered.connect(self.start_recording)
        record_menu.addAction(start_record_action)

        # 暂停录制
        pause_record_action = QAction('暂停录制(&P)', self)
        pause_record_action.triggered.connect(self.pause_recording)
        record_menu.addAction(pause_record_action)

        # 停止录制
        stop_record_action = QAction('停止录制(&T)', self)
        stop_record_action.setShortcut('F11')
        stop_record_action.triggered.connect(self.stop_recording)
        record_menu.addAction(stop_record_action)

        # 播放菜单
        play_menu = menubar.addMenu('播放(&P)')

        # 开始播放
        start_play_action = QAction('开始播放(&S)', self)
        start_play_action.setShortcut('F10')
        start_play_action.triggered.connect(self.start_playback)
        play_menu.addAction(start_play_action)

        # 暂停播放
        pause_play_action = QAction('暂停播放(&P)', self)
        pause_play_action.triggered.connect(self.pause_playback)
        play_menu.addAction(pause_play_action)

        # 停止播放
        stop_play_action = QAction('停止播放(&T)', self)
        stop_play_action.triggered.connect(self.stop_playback)
        play_menu.addAction(stop_play_action)

        # 设置菜单
        settings_menu = menubar.addMenu('设置(&S)')

        # 首选项
        preferences_action = QAction('首选项(&P)', self)
        preferences_action.triggered.connect(self.show_settings)
        settings_menu.addAction(preferences_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")

    def setup_connections(self):
        """设置信号连接"""
        # 控制面板信号
        self.control_panel.record_started.connect(self.start_recording)
        self.control_panel.record_paused.connect(self.pause_recording)
        self.control_panel.record_stopped.connect(self.stop_recording)
        self.control_panel.playback_started.connect(self.start_playback)
        self.control_panel.playback_paused.connect(self.pause_playback)
        self.control_panel.playback_stopped.connect(self.stop_playback)
        self.control_panel.speed_changed.connect(self.change_play_speed)
        self.control_panel.open_file_requested.connect(self.open_recording)
        self.control_panel.save_file_requested.connect(self.save_recording)

        # 播放器回调
        self.player.set_progress_callback(self.on_playback_progress)
        self.player.set_complete_callback(self.on_playback_complete)

    def setup_timer(self):
        """设置定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(100)  # 每100ms更新一次

    def update_status(self):
        """更新状态显示"""
        # 更新录制状态
        record_status = self.recorder.get_recording_status()
        if record_status['is_recording']:
            self.status_bar.showMessage(
                f"录制中... 操作数: {record_status['action_count']}, "
                f"时长: {record_status['duration']:.1f}s"
            )
            self.control_panel.update_record_status(record_status)

        # 更新播放状态
        play_status = self.player.get_playback_status()
        if play_status['is_playing']:
            self.status_bar.showMessage(
                f"播放中... 进度: {play_status['current_index']}/{play_status['total_actions']}"
            )
            self.control_panel.update_playback_status(play_status)

        # 更新操作列表
        if record_status['is_recording']:
            actions = self.recorder.get_actions()
            self.action_list.update_actions(actions)

    # 录制相关方法
    def new_recording(self):
        """新建录制"""
        if self.recorder.get_recording_status()['is_recording']:
            reply = QMessageBox.question(
                self, '确认', '当前正在录制，是否停止并新建？',
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.stop_recording()
            else:
                return

        self.recorder.clear_actions()
        self.action_list.clear_actions()
        self.current_recording_file = None
        self.status_bar.showMessage("新建录制")

    def start_recording(self):
        """开始录制"""
        # 应用鼠标移动设置
        self.recorder.set_mouse_move_settings(
            record_move=self.settings.get('record_mouse_move', True),
            distance_threshold=self.settings.get('mouse_distance_threshold', 5),
            time_threshold=self.settings.get('mouse_time_threshold', 0.05)
        )

        if self.recorder.start_recording():
            self.status_bar.showMessage("开始录制...")
            self.control_panel.set_recording_state(True)
        else:
            QMessageBox.warning(self, '警告', '无法开始录制')

    def pause_recording(self):
        """暂停录制"""
        if self.recorder.pause_recording():
            self.status_bar.showMessage("录制已暂停")

    def stop_recording(self):
        """停止录制"""
        if self.recorder.stop_recording():
            self.status_bar.showMessage("录制已停止")
            self.control_panel.set_recording_state(False)

            # 自动保存
            if self.settings['auto_save']:
                self.save_recording()

    def start_playback(self):
        """开始播放"""
        actions = self.recorder.get_actions()
        if not actions:
            QMessageBox.information(self, '提示', '没有可播放的操作')
            return

        self.player.load_actions(actions)
        if self.player.start_playback(self.settings['play_speed']):
            self.status_bar.showMessage("开始播放...")
            self.control_panel.set_playing_state(True)
        else:
            QMessageBox.warning(self, '警告', '无法开始播放')

    def pause_playback(self):
        """暂停播放"""
        if self.player.pause_playback():
            self.status_bar.showMessage("播放已暂停")

    def stop_playback(self):
        """停止播放"""
        if self.player.stop_playback():
            self.status_bar.showMessage("播放已停止")
            self.control_panel.set_playing_state(False)

    def change_play_speed(self, speed):
        """改变播放速度"""
        self.settings['play_speed'] = speed
        self.player.set_play_speed(speed)

    # 文件操作方法
    def save_recording(self):
        """保存录制"""
        actions = self.recorder.get_actions()
        if not actions:
            QMessageBox.information(self, '提示', '没有可保存的操作')
            return

        try:
            if self.current_recording_file:
                # 覆盖保存
                self.data_manager.save_recording(actions, self.current_recording_file)
                self.status_bar.showMessage(f"已保存到 {self.current_recording_file}")
            else:
                # 另存为
                self.save_recording_as()
        except Exception as e:
            QMessageBox.critical(self, '错误', f'保存失败: {str(e)}')

    def save_recording_as(self):
        """另存为"""
        actions = self.recorder.get_actions()
        if not actions:
            QMessageBox.information(self, '提示', '没有可保存的操作')
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, '保存录制文件', '', 'JSON文件 (*.json)'
        )

        if filename:
            try:
                saved_path = self.data_manager.save_recording(actions, filename)
                self.current_recording_file = filename
                self.status_bar.showMessage(f"已保存到 {saved_path}")
            except Exception as e:
                QMessageBox.critical(self, '错误', f'保存失败: {str(e)}')

    def open_recording(self):
        """打开录制文件"""
        # 设置默认目录为data文件夹
        default_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
        if not os.path.exists(default_dir):
            default_dir = ''

        filename, _ = QFileDialog.getOpenFileName(
            self, '打开录制文件', default_dir,
            'JSON文件 (*.json);;所有文件 (*.*)'
        )

        if filename:
            try:
                # 加载录制数据
                data = self.data_manager.load_recording(filename)
                actions = data['actions']

                if not actions:
                    QMessageBox.information(self, '提示', '该文件中没有录制的操作。')
                    return

                # 清空当前数据
                self.recorder.clear_actions()

                # 加载到播放器和界面
                self.player.load_actions(actions)
                self.action_list.load_actions(actions)

                # 更新文件状态
                self.current_recording_file = filename
                self.add_to_recent_files(filename)

                # 显示文件信息
                file_info = data.get('metadata', {})
                action_count = len(actions)
                duration = actions[-1]['timestamp'] if actions else 0

                info_msg = f"已打开录制文件: {os.path.basename(filename)}\n"
                info_msg += f"操作数量: {action_count}\n"
                info_msg += f"录制时长: {duration:.2f} 秒"

                if file_info.get('created_at'):
                    info_msg += f"\n创建时间: {file_info['created_at']}"

                QMessageBox.information(self, '文件已打开', info_msg)
                self.status_bar.showMessage(f"已打开: {os.path.basename(filename)} ({action_count} 个操作)")

            except FileNotFoundError:
                QMessageBox.critical(self, '错误', f'文件不存在: {filename}')
            except json.JSONDecodeError:
                QMessageBox.critical(self, '错误', f'文件格式错误，不是有效的JSON文件: {filename}')
            except KeyError as e:
                QMessageBox.critical(self, '错误', f'文件格式错误，缺少必要字段: {str(e)}')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'打开文件失败: {str(e)}')

    def import_recording(self):
        """导入录制"""
        filename, _ = QFileDialog.getOpenFileName(
            self, '导入录制文件', '', 'JSON文件 (*.json)'
        )

        if filename:
            try:
                imported_path = self.data_manager.import_recording(filename)
                self.status_bar.showMessage(f"已导入到 {imported_path}")
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导入失败: {str(e)}')

    def export_recording(self):
        """导出录制"""
        if not self.current_recording_file:
            QMessageBox.information(self, '提示', '请先保存当前录制')
            return

        export_path, _ = QFileDialog.getSaveFileName(
            self, '导出录制文件', '', 'JSON文件 (*.json)'
        )

        if export_path:
            try:
                if self.data_manager.export_recording(self.current_recording_file, export_path):
                    self.status_bar.showMessage(f"已导出到 {export_path}")
                else:
                    QMessageBox.warning(self, '警告', '导出失败')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'导出失败: {str(e)}')

    # 回调方法
    def on_playback_progress(self, progress, current, total):
        """播放进度回调"""
        self.control_panel.update_progress(progress)
        self.action_list.highlight_current_action(current - 1)

    def on_playback_complete(self):
        """播放完成回调"""
        self.status_bar.showMessage("播放完成")
        self.control_panel.set_playing_state(False)
        self.action_list.clear_highlight()

    # 设置和帮助
    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self.settings, self)
        if dialog.exec_() == dialog.Accepted:
            self.settings = dialog.get_settings()

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, '关于',
            '操作录制器 v1.0\n\n'
            '一个用于录制和回放鼠标键盘操作的工具\n\n'
            '使用PyQt5和pynput开发'
        )

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.recorder.get_recording_status()['is_recording']:
            reply = QMessageBox.question(
                self, '确认退出', '当前正在录制，是否保存并退出？',
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel
            )

            if reply == QMessageBox.Save:
                self.save_recording()
                event.accept()
            elif reply == QMessageBox.Discard:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    # 最近文件管理
    def add_to_recent_files(self, filename):
        """添加文件到最近文件列表"""
        # 移除已存在的相同文件
        if filename in self.recent_files:
            self.recent_files.remove(filename)

        # 添加到列表开头
        self.recent_files.insert(0, filename)

        # 限制列表长度
        if len(self.recent_files) > self.max_recent_files:
            self.recent_files = self.recent_files[:self.max_recent_files]

        # 更新菜单
        self.update_recent_files_menu()

    def update_recent_files_menu(self):
        """更新最近文件菜单"""
        self.recent_files_menu.clear()

        if not self.recent_files:
            no_files_action = QAction('(无最近文件)', self)
            no_files_action.setEnabled(False)
            self.recent_files_menu.addAction(no_files_action)
            return

        for i, filename in enumerate(self.recent_files):
            if os.path.exists(filename):
                # 显示文件名和序号
                display_name = f"{i+1}. {os.path.basename(filename)}"
                action = QAction(display_name, self)
                action.setToolTip(filename)  # 完整路径作为工具提示
                action.triggered.connect(lambda checked, f=filename: self.open_recent_file(f))
                self.recent_files_menu.addAction(action)
            else:
                # 文件不存在，从列表中移除
                self.recent_files.remove(filename)

        # 添加分隔符和清空选项
        if self.recent_files:
            self.recent_files_menu.addSeparator()
            clear_action = QAction('清空最近文件', self)
            clear_action.triggered.connect(self.clear_recent_files)
            self.recent_files_menu.addAction(clear_action)

    def open_recent_file(self, filename):
        """打开最近的文件"""
        if not os.path.exists(filename):
            QMessageBox.warning(self, '警告', f'文件不存在: {filename}')
            # 从最近文件列表中移除
            if filename in self.recent_files:
                self.recent_files.remove(filename)
                self.update_recent_files_menu()
            return

        try:
            # 加载录制数据
            data = self.data_manager.load_recording(filename)
            actions = data['actions']

            if not actions:
                QMessageBox.information(self, '提示', '该文件中没有录制的操作。')
                return

            # 清空当前数据
            self.recorder.clear_actions()

            # 加载到播放器和界面
            self.player.load_actions(actions)
            self.action_list.load_actions(actions)

            # 更新文件状态
            self.current_recording_file = filename
            self.add_to_recent_files(filename)  # 更新最近文件列表

            # 更新状态栏
            action_count = len(actions)
            self.status_bar.showMessage(f"已打开: {os.path.basename(filename)} ({action_count} 个操作)")

        except Exception as e:
            QMessageBox.critical(self, '错误', f'打开文件失败: {str(e)}')

    def clear_recent_files(self):
        """清空最近文件列表"""
        reply = QMessageBox.question(
            self, '确认', '确定要清空最近文件列表吗？',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.recent_files.clear()
            self.update_recent_files_menu()

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, '关于',
            '操作录制器 v1.0\n\n'
            '一个用于录制和回放鼠标键盘操作的工具\n'
            '基于PyQt5和pynput开发\n\n'
            '功能特性:\n'
            '• 录制鼠标和键盘操作\n'
            '• 智能鼠标移动过滤\n'
            '• 支持打开和保存录制文件\n'
            '• 最近文件快速访问\n'
            '• 可调节播放速度'
        )