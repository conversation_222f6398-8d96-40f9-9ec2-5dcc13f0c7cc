"""
设置对话框UI - 应用程序设置界面
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QWidget, QGroupBox, QCheckBox, QSpinBox,
                             QDoubleSpinBox, QLineEdit, QLabel, QPushButton,
                             QComboBox, QSlider, QFormLayout, QDialogButtonBox)
from PyQt5.QtCore import Qt


class SettingsDialog(QDialog):
    """设置对话框类"""

    def __init__(self, settings, parent=None):
        super().__init__(parent)
        self.settings = settings.copy()

        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(500, 400)

        layout = QVBoxLayout(self)

        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 录制设置标签页
        self.create_record_tab()

        # 播放设置标签页
        self.create_playback_tab()

        # 快捷键设置标签页
        self.create_hotkey_tab()

        # 高级设置标签页
        self.create_advanced_tab()

        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.RestoreDefaults
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.RestoreDefaults).clicked.connect(self.restore_defaults)

        layout.addWidget(button_box)

    def create_record_tab(self):
        """创建录制设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 录制选项组
        record_group = QGroupBox("录制选项")
        record_layout = QFormLayout(record_group)

        # 录制鼠标移动
        self.record_mouse_move_cb = QCheckBox()
        record_layout.addRow("录制鼠标移动:", self.record_mouse_move_cb)

        # 鼠标移动优化组
        mouse_opt_group = QGroupBox("鼠标移动优化")
        mouse_opt_layout = QFormLayout(mouse_opt_group)

        # 距离阈值
        self.mouse_distance_threshold = QSpinBox()
        self.mouse_distance_threshold.setRange(1, 50)
        self.mouse_distance_threshold.setValue(5)
        self.mouse_distance_threshold.setSuffix(" 像素")
        self.mouse_distance_threshold.setToolTip("小于此距离的鼠标移动将被忽略，减少重复数据")
        mouse_opt_layout.addRow("距离阈值:", self.mouse_distance_threshold)

        # 时间阈值
        self.mouse_time_threshold = QDoubleSpinBox()
        self.mouse_time_threshold.setRange(0.01, 1.0)
        self.mouse_time_threshold.setValue(0.05)
        self.mouse_time_threshold.setSingleStep(0.01)
        self.mouse_time_threshold.setSuffix(" 秒")
        self.mouse_time_threshold.setToolTip("此时间内的鼠标移动将被合并，减少数据量")
        mouse_opt_layout.addRow("时间阈值:", self.mouse_time_threshold)

        layout.addWidget(mouse_opt_group)

        # 录制鼠标点击
        self.record_mouse_click_cb = QCheckBox()
        self.record_mouse_click_cb.setChecked(True)
        self.record_mouse_click_cb.setEnabled(False)  # 必须录制
        record_layout.addRow("录制鼠标点击:", self.record_mouse_click_cb)

        # 录制键盘按键
        self.record_keyboard_cb = QCheckBox()
        self.record_keyboard_cb.setChecked(True)
        record_layout.addRow("录制键盘按键:", self.record_keyboard_cb)

        # 录制鼠标滚轮
        self.record_scroll_cb = QCheckBox()
        self.record_scroll_cb.setChecked(True)
        record_layout.addRow("录制鼠标滚轮:", self.record_scroll_cb)

        layout.addWidget(record_group)

        # 自动保存组
        save_group = QGroupBox("保存选项")
        save_layout = QFormLayout(save_group)

        # 自动保存
        self.auto_save_cb = QCheckBox()
        save_layout.addRow("录制完成后自动保存:", self.auto_save_cb)

        # 保存间隔
        self.save_interval_spin = QSpinBox()
        self.save_interval_spin.setMinimum(1)
        self.save_interval_spin.setMaximum(3600)
        self.save_interval_spin.setSuffix(" 秒")
        self.save_interval_spin.setValue(300)
        save_layout.addRow("自动保存间隔:", self.save_interval_spin)

        layout.addWidget(save_group)

        # 过滤选项组
        filter_group = QGroupBox("过滤选项")
        filter_layout = QFormLayout(filter_group)

        # 最小时间间隔
        self.min_interval_spin = QDoubleSpinBox()
        self.min_interval_spin.setMinimum(0.0)
        self.min_interval_spin.setMaximum(1.0)
        self.min_interval_spin.setSingleStep(0.01)
        self.min_interval_spin.setSuffix(" 秒")
        self.min_interval_spin.setValue(0.01)
        filter_layout.addRow("最小操作间隔:", self.min_interval_spin)

        # 鼠标移动阈值
        self.mouse_threshold_spin = QSpinBox()
        self.mouse_threshold_spin.setMinimum(1)
        self.mouse_threshold_spin.setMaximum(100)
        self.mouse_threshold_spin.setSuffix(" 像素")
        self.mouse_threshold_spin.setValue(5)
        filter_layout.addRow("鼠标移动阈值:", self.mouse_threshold_spin)

        layout.addWidget(filter_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "录制设置")

    def create_playback_tab(self):
        """创建播放设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 播放选项组
        play_group = QGroupBox("播放选项")
        play_layout = QFormLayout(play_group)

        # 默认播放速度
        self.default_speed_spin = QDoubleSpinBox()
        self.default_speed_spin.setMinimum(0.1)
        self.default_speed_spin.setMaximum(10.0)
        self.default_speed_spin.setSingleStep(0.1)
        self.default_speed_spin.setSuffix("x")
        self.default_speed_spin.setValue(1.0)
        play_layout.addRow("默认播放速度:", self.default_speed_spin)

        # 播放前延迟
        self.play_delay_spin = QSpinBox()
        self.play_delay_spin.setMinimum(0)
        self.play_delay_spin.setMaximum(10)
        self.play_delay_spin.setSuffix(" 秒")
        self.play_delay_spin.setValue(3)
        play_layout.addRow("播放前延迟:", self.play_delay_spin)

        # 循环播放
        self.loop_playback_cb = QCheckBox()
        play_layout.addRow("循环播放:", self.loop_playback_cb)

        # 播放完成后操作
        self.after_play_combo = QComboBox()
        self.after_play_combo.addItems([
            "无操作", "显示通知", "关闭程序", "最小化窗口"
        ])
        play_layout.addRow("播放完成后:", self.after_play_combo)

        layout.addWidget(play_group)

        # 安全选项组
        safety_group = QGroupBox("安全选项")
        safety_layout = QFormLayout(safety_group)

        # 播放前确认
        self.confirm_play_cb = QCheckBox()
        safety_layout.addRow("播放前确认:", self.confirm_play_cb)

        # 紧急停止键
        self.emergency_stop_edit = QLineEdit()
        self.emergency_stop_edit.setPlaceholderText("按键组合，如 Ctrl+Alt+S")
        safety_layout.addRow("紧急停止键:", self.emergency_stop_edit)

        layout.addWidget(safety_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "播放设置")

    def create_hotkey_tab(self):
        """创建快捷键设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 快捷键组
        hotkey_group = QGroupBox("全局快捷键")
        hotkey_layout = QFormLayout(hotkey_group)

        # 开始/停止录制
        self.hotkey_record_edit = QLineEdit()
        self.hotkey_record_edit.setPlaceholderText("如: F9")
        hotkey_layout.addRow("开始/停止录制:", self.hotkey_record_edit)

        # 开始/停止播放
        self.hotkey_play_edit = QLineEdit()
        self.hotkey_play_edit.setPlaceholderText("如: F10")
        hotkey_layout.addRow("开始/停止播放:", self.hotkey_play_edit)

        # 暂停/恢复
        self.hotkey_pause_edit = QLineEdit()
        self.hotkey_pause_edit.setPlaceholderText("如: F11")
        hotkey_layout.addRow("暂停/恢复:", self.hotkey_pause_edit)

        # 紧急停止
        self.hotkey_stop_edit = QLineEdit()
        self.hotkey_stop_edit.setPlaceholderText("如: Esc")
        hotkey_layout.addRow("紧急停止:", self.hotkey_stop_edit)

        layout.addWidget(hotkey_group)

        # 说明
        info_label = QLabel("""
快捷键说明:
• 支持功能键: F1-F12
• 支持组合键: Ctrl+, Alt+, Shift+
• 示例: Ctrl+F9, Alt+F10, Shift+F11
• 留空表示禁用该快捷键
        """)
        info_label.setStyleSheet("color: gray; font-size: 11px;")
        layout.addWidget(info_label)

        layout.addStretch()

        self.tab_widget.addTab(tab, "快捷键")

    def create_advanced_tab(self):
        """创建高级设置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 性能选项组
        performance_group = QGroupBox("性能选项")
        performance_layout = QFormLayout(performance_group)

        # 更新频率
        self.update_freq_spin = QSpinBox()
        self.update_freq_spin.setMinimum(10)
        self.update_freq_spin.setMaximum(1000)
        self.update_freq_spin.setSuffix(" ms")
        self.update_freq_spin.setValue(100)
        performance_layout.addRow("界面更新频率:", self.update_freq_spin)

        # 最大操作数
        self.max_actions_spin = QSpinBox()
        self.max_actions_spin.setMinimum(100)
        self.max_actions_spin.setMaximum(100000)
        self.max_actions_spin.setValue(10000)
        performance_layout.addRow("最大操作数:", self.max_actions_spin)

        layout.addWidget(performance_group)

        # 日志选项组
        log_group = QGroupBox("日志选项")
        log_layout = QFormLayout(log_group)

        # 启用日志
        self.enable_log_cb = QCheckBox()
        log_layout.addRow("启用日志记录:", self.enable_log_cb)

        # 日志级别
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        log_layout.addRow("日志级别:", self.log_level_combo)

        layout.addWidget(log_group)
        layout.addStretch()

        self.tab_widget.addTab(tab, "高级设置")

    def load_settings(self):
        """加载设置到界面"""
        # 录制设置
        self.record_mouse_move_cb.setChecked(
            self.settings.get('record_mouse_move', True)
        )
        self.mouse_distance_threshold.setValue(
            self.settings.get('mouse_distance_threshold', 5)
        )
        self.mouse_time_threshold.setValue(
            self.settings.get('mouse_time_threshold', 0.05)
        )
        self.record_keyboard_cb.setChecked(
            self.settings.get('record_keyboard', True)
        )
        self.record_scroll_cb.setChecked(
            self.settings.get('record_scroll', True)
        )

        # 保存设置
        self.auto_save_cb.setChecked(
            self.settings.get('auto_save', True)
        )
        self.save_interval_spin.setValue(
            self.settings.get('save_interval', 300)
        )

        # 过滤设置
        self.min_interval_spin.setValue(
            self.settings.get('min_interval', 0.01)
        )
        self.mouse_threshold_spin.setValue(
            self.settings.get('mouse_threshold', 5)
        )

        # 播放设置
        self.default_speed_spin.setValue(
            self.settings.get('play_speed', 1.0)
        )
        self.play_delay_spin.setValue(
            self.settings.get('play_delay', 3)
        )
        self.loop_playback_cb.setChecked(
            self.settings.get('loop_playback', False)
        )
        self.after_play_combo.setCurrentText(
            self.settings.get('after_play', '无操作')
        )

        # 安全设置
        self.confirm_play_cb.setChecked(
            self.settings.get('confirm_play', False)
        )
        self.emergency_stop_edit.setText(
            self.settings.get('emergency_stop', 'Ctrl+Alt+S')
        )

        # 快捷键设置
        self.hotkey_record_edit.setText(
            self.settings.get('hotkey_record', 'F9')
        )
        self.hotkey_play_edit.setText(
            self.settings.get('hotkey_play', 'F10')
        )
        self.hotkey_pause_edit.setText(
            self.settings.get('hotkey_pause', 'F11')
        )
        self.hotkey_stop_edit.setText(
            self.settings.get('hotkey_stop', 'Esc')
        )

        # 高级设置
        self.update_freq_spin.setValue(
            self.settings.get('update_freq', 100)
        )
        self.max_actions_spin.setValue(
            self.settings.get('max_actions', 10000)
        )
        self.enable_log_cb.setChecked(
            self.settings.get('enable_log', False)
        )
        self.log_level_combo.setCurrentText(
            self.settings.get('log_level', 'INFO')
        )

    def get_settings(self):
        """从界面获取设置"""
        settings = {}

        # 录制设置
        settings['record_mouse_move'] = self.record_mouse_move_cb.isChecked()
        settings['mouse_distance_threshold'] = self.mouse_distance_threshold.value()
        settings['mouse_time_threshold'] = self.mouse_time_threshold.value()
        settings['record_keyboard'] = self.record_keyboard_cb.isChecked()
        settings['record_scroll'] = self.record_scroll_cb.isChecked()

        # 保存设置
        settings['auto_save'] = self.auto_save_cb.isChecked()
        settings['save_interval'] = self.save_interval_spin.value()

        # 过滤设置
        settings['min_interval'] = self.min_interval_spin.value()
        settings['mouse_threshold'] = self.mouse_threshold_spin.value()

        # 播放设置
        settings['play_speed'] = self.default_speed_spin.value()
        settings['play_delay'] = self.play_delay_spin.value()
        settings['loop_playback'] = self.loop_playback_cb.isChecked()
        settings['after_play'] = self.after_play_combo.currentText()

        # 安全设置
        settings['confirm_play'] = self.confirm_play_cb.isChecked()
        settings['emergency_stop'] = self.emergency_stop_edit.text()

        # 快捷键设置
        settings['hotkey_record'] = self.hotkey_record_edit.text()
        settings['hotkey_play'] = self.hotkey_play_edit.text()
        settings['hotkey_pause'] = self.hotkey_pause_edit.text()
        settings['hotkey_stop'] = self.hotkey_stop_edit.text()

        # 高级设置
        settings['update_freq'] = self.update_freq_spin.value()
        settings['max_actions'] = self.max_actions_spin.value()
        settings['enable_log'] = self.enable_log_cb.isChecked()
        settings['log_level'] = self.log_level_combo.currentText()

        return settings

    def restore_defaults(self):
        """恢复默认设置"""
        default_settings = {
            'record_mouse_move': True,
            'mouse_distance_threshold': 5,
            'mouse_time_threshold': 0.05,
            'record_keyboard': True,
            'record_scroll': True,
            'auto_save': True,
            'save_interval': 300,
            'min_interval': 0.01,
            'mouse_threshold': 5,
            'play_speed': 1.0,
            'play_delay': 3,
            'loop_playback': False,
            'after_play': '无操作',
            'confirm_play': False,
            'emergency_stop': 'Ctrl+Alt+S',
            'hotkey_record': 'F9',
            'hotkey_play': 'F10',
            'hotkey_pause': 'F11',
            'hotkey_stop': 'Esc',
            'update_freq': 100,
            'max_actions': 10000,
            'enable_log': False,
            'log_level': 'INFO'
        }

        self.settings = default_settings
        self.load_settings()