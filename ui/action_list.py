"""
操作列表UI - 显示和管理录制的操作序列
"""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget,
                             QTreeWidgetItem, QHeaderView, QPushButton,
                             QLabel, QLineEdit, QComboBox, QGroupBox,
                             QMenu, QAction, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QBrush


class ActionListWidget(QWidget):
    """操作列表组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.actions = []
        self.filtered_actions = []
        self.current_highlight = -1

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建过滤器组
        self.create_filter_group(layout)

        # 创建操作树
        self.create_action_tree(layout)

        # 创建操作按钮
        self.create_action_buttons(layout)

    def create_filter_group(self, parent_layout):
        """创建过滤器组"""
        group = QGroupBox("操作过滤")
        layout = QHBoxLayout(group)

        # 搜索框
        layout.addWidget(QLabel("搜索:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索...")
        layout.addWidget(self.search_edit)

        # 类型过滤
        layout.addWidget(QLabel("类型:"))
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "全部", "鼠标点击", "鼠标移动", "鼠标滚轮", "键盘按键"
        ])
        layout.addWidget(self.type_combo)

        # 清除过滤器按钮
        self.clear_filter_btn = QPushButton("清除过滤")
        layout.addWidget(self.clear_filter_btn)

        parent_layout.addWidget(group)

    def create_action_tree(self, parent_layout):
        """创建操作树"""
        # 标题
        title_layout = QHBoxLayout()
        title_label = QLabel("操作序列")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_layout.addWidget(title_label)

        self.count_label = QLabel("(0 个操作)")
        title_layout.addWidget(self.count_label)
        title_layout.addStretch()

        parent_layout.addLayout(title_layout)

        # 树控件
        self.tree = QTreeWidget()
        self.tree.setHeaderLabels([
            "序号", "时间", "类型", "详细信息", "坐标"
        ])

        # 设置列宽
        header = self.tree.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 序号
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 时间
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 类型
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # 详细信息
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 坐标

        # 设置样式
        self.tree.setAlternatingRowColors(True)
        self.tree.setRootIsDecorated(False)
        self.tree.setSelectionBehavior(QTreeWidget.SelectRows)

        # 设置右键菜单
        self.tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree.customContextMenuRequested.connect(self.show_context_menu)

        parent_layout.addWidget(self.tree)

    def create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()

        self.delete_btn = QPushButton("删除选中")
        self.delete_btn.setEnabled(False)

        self.clear_btn = QPushButton("清空列表")

        self.export_btn = QPushButton("导出选中")
        self.export_btn.setEnabled(False)

        button_layout.addWidget(self.delete_btn)
        button_layout.addWidget(self.clear_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addStretch()

        parent_layout.addLayout(button_layout)

    def setup_connections(self):
        """设置信号连接"""
        # 过滤器
        self.search_edit.textChanged.connect(self.apply_filter)
        self.type_combo.currentTextChanged.connect(self.apply_filter)
        self.clear_filter_btn.clicked.connect(self.clear_filter)

        # 树选择
        self.tree.itemSelectionChanged.connect(self.on_selection_changed)

        # 按钮
        self.delete_btn.clicked.connect(self.delete_selected)
        self.clear_btn.clicked.connect(self.clear_actions)
        self.export_btn.clicked.connect(self.export_selected)

    def update_actions(self, actions):
        """更新操作列表"""
        self.actions = actions.copy()
        self.apply_filter()

    def load_actions(self, actions):
        """加载操作列表"""
        self.actions = actions.copy()
        self.apply_filter()

    def apply_filter(self):
        """应用过滤器"""
        search_text = self.search_edit.text().lower()
        type_filter = self.type_combo.currentText()

        # 过滤操作
        self.filtered_actions = []
        for action in self.actions:
            # 类型过滤
            if type_filter != "全部":
                action_type = self.get_action_type_display(action['type'])
                if action_type != type_filter:
                    continue

            # 搜索过滤
            if search_text:
                action_text = self.get_action_text(action).lower()
                if search_text not in action_text:
                    continue

            self.filtered_actions.append(action)

        self.refresh_tree()

    def refresh_tree(self):
        """刷新树显示"""
        self.tree.clear()

        for i, action in enumerate(self.filtered_actions):
            item = QTreeWidgetItem()

            # 序号
            item.setText(0, str(i + 1))

            # 时间
            timestamp = action.get('timestamp', 0)
            item.setText(1, f"{timestamp:.3f}s")

            # 类型
            action_type = self.get_action_type_display(action['type'])
            item.setText(2, action_type)

            # 详细信息
            detail = self.get_action_detail(action)
            item.setText(3, detail)

            # 坐标
            coords = self.get_action_coordinates(action)
            item.setText(4, coords)

            # 设置数据
            item.setData(0, Qt.UserRole, action)

            self.tree.addTopLevelItem(item)

        # 更新计数
        self.count_label.setText(f"({len(self.filtered_actions)} 个操作)")

    def get_action_type_display(self, action_type):
        """获取操作类型显示名称"""
        type_map = {
            'mouse_click': '鼠标点击',
            'mouse_move': '鼠标移动',
            'mouse_scroll': '鼠标滚轮',
            'key_press': '键盘按键'
        }
        return type_map.get(action_type, action_type)

    def get_action_detail(self, action):
        """获取操作详细信息"""
        action_type = action['type']

        if action_type == 'mouse_click':
            button = action.get('button', 'unknown')
            pressed = action.get('pressed', False)
            state = "按下" if pressed else "释放"
            return f"{button} {state}"

        elif action_type == 'mouse_move':
            return "鼠标移动"

        elif action_type == 'mouse_scroll':
            dx = action.get('dx', 0)
            dy = action.get('dy', 0)
            return f"滚轮 dx:{dx} dy:{dy}"

        elif action_type == 'key_press':
            key = action.get('key', 'unknown')
            pressed = action.get('pressed', False)
            state = "按下" if pressed else "释放"
            return f"按键 {key} {state}"

        return "未知操作"

    def get_action_coordinates(self, action):
        """获取操作坐标"""
        x = action.get('x')
        y = action.get('y')

        if x is not None and y is not None:
            return f"({x}, {y})"
        return ""

    def get_action_text(self, action):
        """获取操作文本（用于搜索）"""
        parts = [
            self.get_action_type_display(action['type']),
            self.get_action_detail(action),
            self.get_action_coordinates(action)
        ]
        return " ".join(filter(None, parts))

    def highlight_current_action(self, index):
        """高亮当前播放的操作"""
        # 清除之前的高亮
        self.clear_highlight()

        if 0 <= index < self.tree.topLevelItemCount():
            item = self.tree.topLevelItem(index)
            # 设置背景色
            for col in range(self.tree.columnCount()):
                item.setBackground(col, QBrush(QColor(255, 255, 0, 100)))

            # 滚动到当前项
            self.tree.scrollToItem(item)
            self.current_highlight = index

    def clear_highlight(self):
        """清除高亮"""
        if self.current_highlight >= 0 and self.current_highlight < self.tree.topLevelItemCount():
            item = self.tree.topLevelItem(self.current_highlight)
            # 清除背景色
            for col in range(self.tree.columnCount()):
                item.setBackground(col, QBrush())

        self.current_highlight = -1

    def clear_filter(self):
        """清除过滤器"""
        self.search_edit.clear()
        self.type_combo.setCurrentIndex(0)

    def on_selection_changed(self):
        """选择改变事件"""
        selected_items = self.tree.selectedItems()
        has_selection = len(selected_items) > 0

        self.delete_btn.setEnabled(has_selection)
        self.export_btn.setEnabled(has_selection)

    def delete_selected(self):
        """删除选中的操作"""
        selected_items = self.tree.selectedItems()
        if not selected_items:
            return

        reply = QMessageBox.question(
            self, '确认删除',
            f'确定要删除选中的 {len(selected_items)} 个操作吗？',
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 获取要删除的操作
            actions_to_delete = []
            for item in selected_items:
                action = item.data(0, Qt.UserRole)
                actions_to_delete.append(action)

            # 从原始列表中删除
            for action in actions_to_delete:
                if action in self.actions:
                    self.actions.remove(action)

            # 刷新显示
            self.apply_filter()

    def clear_actions(self):
        """清空操作列表"""
        if not self.actions:
            return

        reply = QMessageBox.question(
            self, '确认清空', '确定要清空所有操作吗？',
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.actions.clear()
            self.apply_filter()

    def export_selected(self):
        """导出选中的操作"""
        selected_items = self.tree.selectedItems()
        if not selected_items:
            return

        # 获取选中的操作
        selected_actions = []
        for item in selected_items:
            action = item.data(0, Qt.UserRole)
            selected_actions.append(action)

        # 这里可以实现导出功能
        QMessageBox.information(
            self, '导出',
            f'将导出 {len(selected_actions)} 个操作\n（功能待实现）'
        )

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.tree.itemAt(position)
        if not item:
            return

        menu = QMenu(self)

        # 删除操作
        delete_action = QAction("删除", self)
        delete_action.triggered.connect(lambda: self.delete_item(item))
        menu.addAction(delete_action)

        # 复制信息
        copy_action = QAction("复制信息", self)
        copy_action.triggered.connect(lambda: self.copy_item_info(item))
        menu.addAction(copy_action)

        menu.addSeparator()

        # 查看详情
        detail_action = QAction("查看详情", self)
        detail_action.triggered.connect(lambda: self.show_item_detail(item))
        menu.addAction(detail_action)

        menu.exec_(self.tree.mapToGlobal(position))

    def delete_item(self, item):
        """删除单个项目"""
        action = item.data(0, Qt.UserRole)
        if action in self.actions:
            self.actions.remove(action)
        self.apply_filter()

    def copy_item_info(self, item):
        """复制项目信息"""
        from PyQt5.QtWidgets import QApplication

        action = item.data(0, Qt.UserRole)
        info_text = f"""操作类型: {self.get_action_type_display(action['type'])}
时间戳: {action.get('timestamp', 0):.3f}s
详细信息: {self.get_action_detail(action)}
坐标: {self.get_action_coordinates(action)}"""

        clipboard = QApplication.clipboard()
        clipboard.setText(info_text)

    def show_item_detail(self, item):
        """显示项目详情"""
        action = item.data(0, Qt.UserRole)

        detail_text = f"""操作详细信息:

类型: {self.get_action_type_display(action['type'])}
时间戳: {action.get('timestamp', 0):.3f}s
详细信息: {self.get_action_detail(action)}
坐标: {self.get_action_coordinates(action)}

原始数据:
{action}"""

        QMessageBox.information(self, '操作详情', detail_text)