#!/usr/bin/env python3
"""
操作录制器主程序
Operation Recorder - 自动录制和回放鼠标键盘操作的工具

使用方法:
    python main.py

功能特性:
- 录制鼠标点击、移动、滚轮操作
- 录制键盘按键操作
- 自动回放录制的操作序列
- 保存和加载录制文件
- 可视化操作序列编辑
- 支持播放速度调节
- 全局快捷键控制

作者: AI Assistant
版本: 1.0
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# 尝试导入qRegisterMetaType，如果失败则忽略
try:
    from PyQt5.QtCore import qRegisterMetaType
except ImportError:
    # 某些PyQt5版本可能没有这个函数，定义一个空函数
    def qRegisterMetaType(type_name):
        pass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow


def check_dependencies():
    """检查依赖库是否安装"""
    missing_deps = []

    try:
        import PyQt5
    except ImportError:
        missing_deps.append("PyQt5")

    try:
        import pynput
    except ImportError:
        missing_deps.append("pynput")

    if missing_deps:
        print("缺少以下依赖库:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False

    return True


def check_permissions():
    """检查系统权限"""
    import platform

    if platform.system() == "Darwin":  # macOS
        print("检测到macOS系统，跳过权限检查以避免崩溃...")
        print("⚠️  注意: 如果录制功能不工作，请手动授予权限:")
        print("1. 打开 系统偏好设置 > 安全性与隐私 > 隐私")
        print("2. 在左侧选择 '辅助功能'")
        print("3. 点击锁图标并输入密码")
        print("4. 添加以下路径:")

        # 显示当前Python解释器路径
        python_path = sys.executable
        print(f"   - Python解释器: {python_path}")
        print("   - 终端应用: /Applications/Utilities/Terminal.app")
        print("5. 勾选添加的应用程序")
        print("6. 重新启动应用程序")

        return True  # 跳过实际的权限检查
    else:
        # Windows/Linux - 保持原有的检查逻辑
        try:
            from pynput import mouse, keyboard

            def test_mouse(x, y):
                _ = x, y  # 忽略未使用的参数

            def test_keyboard(key):
                _ = key  # 忽略未使用的参数

            mouse_listener = mouse.Listener(on_move=test_mouse)
            keyboard_listener = keyboard.Listener(on_press=test_keyboard)

            # 简单测试
            mouse_listener.start()
            mouse_listener.stop()
            keyboard_listener.start()
            keyboard_listener.stop()

            print("✓ 权限检查通过")
            return True

        except Exception as e:
            print(f"权限检查失败: {e}")
            if platform.system() == "Windows":
                print("在Windows上，可能需要以管理员身份运行程序")
            elif platform.system() == "Linux":
                print("在Linux上，可能需要安装额外的包:")
                print("sudo apt-get install python3-tk python3-dev")
            return False


def main():
    """主函数"""
    print("操作录制器 v1.0")
    print("=" * 50)

    # 检查依赖
    if not check_dependencies():
        return 1

    # 检查权限
    if not check_permissions():
        print("\n警告: 权限检查失败，某些功能可能无法正常工作")
        response = input("是否继续启动? (y/N): ")
        if response.lower() != 'y':
            return 1

    # 设置高DPI支持（必须在创建QApplication之前）
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 设置绘图优化属性
    QApplication.setAttribute(Qt.AA_UseDesktopOpenGL, True)
    QApplication.setAttribute(Qt.AA_ShareOpenGLContexts, True)

    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("操作录制器")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("AI Assistant")

    # 注册Qt元类型以避免警告
    try:
        qRegisterMetaType("QVector<int>")
        qRegisterMetaType("QList<int>")
    except Exception:
        # 如果注册失败，忽略错误（某些PyQt5版本可能不支持）
        pass

    try:
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()

        print("应用程序启动成功!")
        print("使用快捷键:")
        print("  F9  - 开始/停止录制")
        print("  F10 - 开始/停止播放")
        print("  F11 - 暂停/恢复")
        print("\n注意: 录制时请确保应用程序有足够的系统权限")

        # 运行应用程序
        return app.exec_()

    except Exception as e:
        QMessageBox.critical(
            None, "启动错误",
            f"应用程序启动失败:\n{str(e)}"
        )
        return 1


if __name__ == "__main__":
    sys.exit(main())