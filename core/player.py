"""
播放器模块 - 负责回放录制的操作序列
"""
import time
import threading
from typing import List, Dict, Any, Callable, Optional
from pynput import mouse, keyboard
from pynput.mouse import But<PERSON>
from pynput.keyboard import Key


class ActionPlayer:
    """操作播放器类"""

    def __init__(self):
        self.actions: List[Dict[str, Any]] = []
        self.is_playing = False
        self.is_paused = False
        self.current_index = 0
        self.play_thread = None
        self.mouse_controller = mouse.Controller()
        self.keyboard_controller = keyboard.Controller()
        self.play_speed = 1.0  # 播放速度倍率
        self.on_progress_callback: Optional[Callable] = None
        self.on_complete_callback: Optional[Callable] = None

    def load_actions(self, actions: List[Dict[str, Any]]):
        """加载操作序列"""
        self.actions = actions.copy()
        self.current_index = 0

    def start_playback(self, speed: float = 1.0):
        """开始播放"""
        if self.is_playing or not self.actions:
            return False

        self.is_playing = True
        self.is_paused = False
        self.play_speed = speed
        self.current_index = 0

        # 在新线程中播放
        self.play_thread = threading.Thread(target=self._play_actions)
        self.play_thread.daemon = True
        self.play_thread.start()

        return True

    def pause_playback(self):
        """暂停播放"""
        if self.is_playing and not self.is_paused:
            self.is_paused = True
            return True
        return False

    def resume_playback(self):
        """恢复播放"""
        if self.is_playing and self.is_paused:
            self.is_paused = False
            return True
        return False

    def stop_playback(self):
        """停止播放"""
        if self.is_playing:
            self.is_playing = False
            self.is_paused = False
            return True
        return False

    def set_play_speed(self, speed: float):
        """设置播放速度"""
        self.play_speed = max(0.1, min(10.0, speed))  # 限制在0.1x到10x之间

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.on_progress_callback = callback

    def set_complete_callback(self, callback: Callable):
        """设置完成回调函数"""
        self.on_complete_callback = callback

    def _play_actions(self):
        """播放操作序列的主循环"""
        start_time = time.time()

        while self.current_index < len(self.actions) and self.is_playing:
            # 处理暂停
            while self.is_paused and self.is_playing:
                time.sleep(0.1)

            if not self.is_playing:
                break

            action = self.actions[self.current_index]

            # 计算等待时间
            if self.current_index > 0:
                prev_timestamp = self.actions[self.current_index - 1]['timestamp']
                current_timestamp = action['timestamp']
                wait_time = (current_timestamp - prev_timestamp) / self.play_speed

                if wait_time > 0:
                    time.sleep(wait_time)

            # 执行操作
            self._execute_action(action)

            # 更新进度
            self.current_index += 1
            if self.on_progress_callback:
                progress = self.current_index / len(self.actions)
                self.on_progress_callback(progress, self.current_index, len(self.actions))

        # 播放完成
        self.is_playing = False
        self.is_paused = False
        if self.on_complete_callback:
            self.on_complete_callback()

    def _execute_action(self, action: Dict[str, Any]):
        """执行单个操作"""
        action_type = action['type']

        try:
            if action_type == 'mouse_move':
                self.mouse_controller.position = (action['x'], action['y'])

            elif action_type == 'mouse_click':
                x, y = action['x'], action['y']
                button_name = action['button']
                pressed = action['pressed']

                # 移动到点击位置
                self.mouse_controller.position = (x, y)

                # 获取按钮对象
                button = getattr(Button, button_name, Button.left)

                if pressed:
                    self.mouse_controller.press(button)
                else:
                    self.mouse_controller.release(button)

            elif action_type == 'mouse_scroll':
                x, y = action['x'], action['y']
                dx, dy = action['dx'], action['dy']

                self.mouse_controller.position = (x, y)
                self.mouse_controller.scroll(dx, dy)

            elif action_type == 'key_press':
                key_name = action['key']
                pressed = action['pressed']

                key = self._parse_key(key_name)
                if key:
                    if pressed:
                        self.keyboard_controller.press(key)
                    else:
                        self.keyboard_controller.release(key)

        except Exception as e:
            print(f"执行操作时出错: {e}")

    def _parse_key(self, key_name: str):
        """解析按键名称为按键对象"""
        try:
            # 处理特殊按键
            if key_name.startswith('Key.'):
                key_attr = key_name.replace('Key.', '')
                return getattr(Key, key_attr, None)

            # 处理普通字符
            if len(key_name) == 1:
                return key_name

            # 处理其他情况
            return key_name

        except Exception:
            return None

    def get_playback_status(self) -> Dict[str, Any]:
        """获取播放状态"""
        return {
            'is_playing': self.is_playing,
            'is_paused': self.is_paused,
            'current_index': self.current_index,
            'total_actions': len(self.actions),
            'progress': self.current_index / len(self.actions) if self.actions else 0,
            'play_speed': self.play_speed
        }