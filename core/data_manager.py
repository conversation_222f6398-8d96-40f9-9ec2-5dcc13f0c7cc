"""
数据管理模块 - 负责操作序列的保存、加载和管理
"""
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path


class DataManager:
    """数据管理器类"""

    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)

    def save_recording(self, actions: List[Dict[str, Any]],
                      filename: Optional[str] = None,
                      metadata: Optional[Dict[str, Any]] = None) -> str:
        """保存录制数据"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"recording_{timestamp}.json"

        if not filename.endswith('.json'):
            filename += '.json'

        # 准备保存的数据
        recording_data = {
            'metadata': {
                'created_at': datetime.now().isoformat(),
                'action_count': len(actions),
                'duration': actions[-1]['timestamp'] if actions else 0,
                'version': '1.0',
                **(metadata or {})
            },
            'actions': actions
        }

        # 保存到文件
        file_path = self.data_dir / filename
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(recording_data, f, indent=2, ensure_ascii=False)
            return str(file_path)
        except Exception as e:
            raise Exception(f"保存录制文件失败: {e}")

    def load_recording(self, filename: str) -> Dict[str, Any]:
        """加载录制数据"""
        file_path = self.data_dir / filename

        if not file_path.exists():
            raise FileNotFoundError(f"录制文件不存在: {filename}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 验证数据格式
            if 'actions' not in data:
                raise ValueError("无效的录制文件格式")

            return data
        except json.JSONDecodeError as e:
            raise Exception(f"录制文件格式错误: {e}")
        except Exception as e:
            raise Exception(f"加载录制文件失败: {e}")

    def list_recordings(self) -> List[Dict[str, Any]]:
        """列出所有录制文件"""
        recordings = []

        for file_path in self.data_dir.glob("*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                metadata = data.get('metadata', {})
                recordings.append({
                    'filename': file_path.name,
                    'filepath': str(file_path),
                    'created_at': metadata.get('created_at', ''),
                    'action_count': metadata.get('action_count', 0),
                    'duration': metadata.get('duration', 0),
                    'title': metadata.get('title', file_path.stem)
                })
            except Exception:
                # 跳过无效文件
                continue

        # 按创建时间排序
        recordings.sort(key=lambda x: x['created_at'], reverse=True)
        return recordings

    def delete_recording(self, filename: str) -> bool:
        """删除录制文件"""
        file_path = self.data_dir / filename

        if not file_path.exists():
            return False

        try:
            file_path.unlink()
            return True
        except Exception:
            return False

    def export_recording(self, filename: str, export_path: str) -> bool:
        """导出录制文件"""
        source_path = self.data_dir / filename

        if not source_path.exists():
            return False

        try:
            import shutil
            shutil.copy2(source_path, export_path)
            return True
        except Exception:
            return False

    def import_recording(self, import_path: str, new_filename: Optional[str] = None) -> str:
        """导入录制文件"""
        import_path = Path(import_path)

        if not import_path.exists():
            raise FileNotFoundError("导入文件不存在")

        # 验证文件格式
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if 'actions' not in data:
                raise ValueError("无效的录制文件格式")
        except Exception as e:
            raise Exception(f"导入文件格式错误: {e}")

        # 确定目标文件名
        if not new_filename:
            new_filename = import_path.name

        if not new_filename.endswith('.json'):
            new_filename += '.json'

        # 避免文件名冲突
        target_path = self.data_dir / new_filename
        counter = 1
        while target_path.exists():
            name_part = new_filename.replace('.json', '')
            new_filename = f"{name_part}_{counter}.json"
            target_path = self.data_dir / new_filename
            counter += 1

        # 复制文件
        try:
            import shutil
            shutil.copy2(import_path, target_path)
            return str(target_path)
        except Exception as e:
            raise Exception(f"导入文件失败: {e}")

    def get_recording_info(self, filename: str) -> Dict[str, Any]:
        """获取录制文件信息"""
        data = self.load_recording(filename)
        metadata = data.get('metadata', {})
        actions = data.get('actions', [])

        # 统计操作类型
        action_types = {}
        for action in actions:
            action_type = action.get('type', 'unknown')
            action_types[action_type] = action_types.get(action_type, 0) + 1

        return {
            'filename': filename,
            'metadata': metadata,
            'action_count': len(actions),
            'action_types': action_types,
            'duration': metadata.get('duration', 0),
            'file_size': os.path.getsize(self.data_dir / filename)
        }