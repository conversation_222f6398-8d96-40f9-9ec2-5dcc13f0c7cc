"""
录制器模块 - 负责监听和记录用户的鼠标键盘操作
"""
import time
import json
from typing import List, Dict, Any, Optional
from pynput import mouse, keyboard
from pynput.mouse import Button
from pynput.keyboard import Key
import threading


class ActionRecorder:
    """操作录制器类"""

    def __init__(self):
        self.actions: List[Dict[str, Any]] = []
        self.is_recording = False
        self.start_time = 0
        self.mouse_listener = None
        self.keyboard_listener = None
        self.recording_thread = None

        # 鼠标移动优化参数
        self.last_mouse_pos = None
        self.last_mouse_time = 0
        self.mouse_move_threshold = 5  # 像素阈值：移动距离小于此值时忽略
        self.mouse_time_threshold = 0.05  # 时间阈值：50ms内的移动合并
        self.record_mouse_move = True  # 是否录制鼠标移动

    def start_recording(self):
        """开始录制"""
        if self.is_recording:
            return False

        self.actions.clear()
        self.is_recording = True
        self.start_time = time.time()

        # 启动鼠标监听器
        self.mouse_listener = mouse.Listener(
            on_move=self._on_mouse_move,
            on_click=self._on_mouse_click,
            on_scroll=self._on_mouse_scroll
        )

        # 启动键盘监听器
        self.keyboard_listener = keyboard.Listener(
            on_press=self._on_key_press,
            on_release=self._on_key_release
        )

        self.mouse_listener.start()
        self.keyboard_listener.start()

        return True

    def stop_recording(self):
        """停止录制"""
        if not self.is_recording:
            return False

        self.is_recording = False

        if self.mouse_listener:
            self.mouse_listener.stop()
        if self.keyboard_listener:
            self.keyboard_listener.stop()

        return True

    def pause_recording(self):
        """暂停录制"""
        if self.is_recording:
            self.is_recording = False
            return True
        return False

    def resume_recording(self):
        """恢复录制"""
        if not self.is_recording:
            self.is_recording = True
            return True
        return False

    def _get_timestamp(self) -> float:
        """获取相对时间戳"""
        return time.time() - self.start_time

    def _on_mouse_move(self, x, y):
        """鼠标移动事件 - 智能过滤重复移动"""
        if not self.is_recording or not self.record_mouse_move:
            return

        current_time = self._get_timestamp()

        # 如果有上一个位置，检查是否需要过滤
        if self.last_mouse_pos is not None:
            last_x, last_y = self.last_mouse_pos

            # 计算移动距离
            distance = ((x - last_x) ** 2 + (y - last_y) ** 2) ** 0.5
            time_diff = current_time - self.last_mouse_time

            # 过滤条件：
            # 1. 移动距离太小
            # 2. 时间间隔太短
            if distance < self.mouse_move_threshold or time_diff < self.mouse_time_threshold:
                # 更新最后位置但不记录
                self.last_mouse_pos = (x, y)
                self.last_mouse_time = current_time
                return

        # 记录有效的鼠标移动
        action = {
            'type': 'mouse_move',
            'timestamp': current_time,
            'x': x,
            'y': y
        }
        self.actions.append(action)

        # 更新最后位置和时间
        self.last_mouse_pos = (x, y)
        self.last_mouse_time = current_time

    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件"""
        if self.is_recording:
            action = {
                'type': 'mouse_click',
                'timestamp': self._get_timestamp(),
                'x': x,
                'y': y,
                'button': button.name,
                'pressed': pressed
            }
            self.actions.append(action)

    def _on_mouse_scroll(self, x, y, dx, dy):
        """鼠标滚轮事件"""
        if self.is_recording:
            action = {
                'type': 'mouse_scroll',
                'timestamp': self._get_timestamp(),
                'x': x,
                'y': y,
                'dx': dx,
                'dy': dy
            }
            self.actions.append(action)

    def _on_key_press(self, key):
        """按键按下事件"""
        if self.is_recording:
            key_name = self._get_key_name(key)
            action = {
                'type': 'key_press',
                'timestamp': self._get_timestamp(),
                'key': key_name,
                'pressed': True
            }
            self.actions.append(action)

    def _on_key_release(self, key):
        """按键释放事件"""
        if self.is_recording:
            key_name = self._get_key_name(key)
            action = {
                'type': 'key_press',
                'timestamp': self._get_timestamp(),
                'key': key_name,
                'pressed': False
            }
            self.actions.append(action)

    def _get_key_name(self, key) -> str:
        """获取按键名称"""
        try:
            return key.char if hasattr(key, 'char') and key.char else str(key)
        except AttributeError:
            return str(key)

    def get_actions(self) -> List[Dict[str, Any]]:
        """获取录制的操作列表"""
        return self.actions.copy()

    def clear_actions(self):
        """清空操作列表"""
        self.actions.clear()
        # 重置鼠标移动状态
        self.last_mouse_pos = None
        self.last_mouse_time = 0

    def get_recording_status(self) -> Dict[str, Any]:
        """获取录制状态"""
        return {
            'is_recording': self.is_recording,
            'action_count': len(self.actions),
            'duration': self._get_timestamp() if self.is_recording else 0
        }

    def set_mouse_move_settings(self, record_move: bool = True,
                               distance_threshold: int = 5,
                               time_threshold: float = 0.05):
        """
        设置鼠标移动录制参数

        Args:
            record_move: 是否录制鼠标移动
            distance_threshold: 距离阈值（像素），小于此值的移动将被忽略
            time_threshold: 时间阈值（秒），此时间内的移动将被合并
        """
        self.record_mouse_move = record_move
        self.mouse_move_threshold = distance_threshold
        self.mouse_time_threshold = time_threshold

    def get_mouse_move_settings(self) -> Dict[str, Any]:
        """获取鼠标移动录制设置"""
        return {
            'record_move': self.record_mouse_move,
            'distance_threshold': self.mouse_move_threshold,
            'time_threshold': self.mouse_time_threshold
        }