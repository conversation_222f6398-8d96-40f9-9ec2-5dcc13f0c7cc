# 操作录制器 (Operation Recorder)

一个用Python开发的自动录制和回放鼠标键盘操作的桌面应用程序。

## 功能特性

### 🎯 核心功能
- **录制操作**: 实时录制鼠标点击、移动、滚轮和键盘按键操作
- **自动回放**: 精确回放录制的操作序列，支持速度调节
- **文件管理**: 保存、加载、导入、导出录制文件
- **可视化编辑**: 查看、编辑、删除操作序列中的单个操作

### 🎮 用户界面
- **直观的控制面板**: 录制和播放控制按钮
- **详细的操作列表**: 显示所有录制的操作，支持搜索和过滤
- **丰富的设置选项**: 录制选项、播放设置、快捷键配置
- **实时状态显示**: 录制状态、播放进度、操作统计

### ⚡ 高级特性
- **全局快捷键**: 支持F9录制、F10播放、F11暂停等快捷键
- **播放速度控制**: 0.1x到10x的播放速度调节
- **操作过滤**: 可选择录制鼠标移动、点击、滚轮、键盘等
- **安全机制**: 播放前确认、紧急停止键等安全选项

## 系统要求

- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Python版本**: Python 3.7+
- **依赖库**: PyQt5, pynput

## 安装说明

### 1. 克隆项目
```bash
git clone <repository-url>
cd DeskApp
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序
```bash
python main.py
```

## 权限设置

### macOS
在macOS上首次运行时，需要授予辅助功能权限：

1. 打开 **系统偏好设置** > **安全性与隐私** > **隐私**
2. 在左侧选择 **辅助功能**
3. 点击锁图标并输入密码
4. 添加并勾选您的终端应用程序或Python解释器
5. 重新启动应用程序

### Windows
在Windows上可能需要以管理员身份运行，以获得完整的系统访问权限。

### Linux
在某些Linux发行版上，可能需要安装额外的包：
```bash
sudo apt-get install python3-tk python3-dev
```

## 使用指南

### 基本操作

1. **开始录制**
   - 点击"开始录制"按钮或按F9
   - 执行您想要录制的操作
   - 点击"停止录制"或再次按F9停止

2. **回放操作**
   - 点击"开始播放"按钮或按F10
   - 程序将自动执行录制的操作
   - 可以随时暂停或停止播放

3. **保存和加载**
   - 使用菜单栏的"文件"菜单保存录制
   - 支持JSON格式的录制文件
   - 可以加载之前保存的录制文件

### 高级功能

#### 操作过滤和编辑
- 在操作列表中搜索特定操作
- 按类型过滤操作（鼠标点击、移动、键盘等）
- 右键点击操作项查看详情或删除

#### 播放设置
- 调节播放速度（0.1x - 10x）
- 设置播放前延迟时间
- 配置播放完成后的操作

#### 快捷键配置
- 自定义全局快捷键
- 设置紧急停止键
- 支持组合键（Ctrl+, Alt+, Shift+）

## 项目结构

```
DeskApp/
├── main.py              # 主程序入口
├── requirements.txt     # 依赖库列表
├── README.md           # 项目说明
├── core/               # 核心功能模块
│   ├── __init__.py
│   ├── recorder.py     # 录制器
│   ├── player.py       # 播放器
│   └── data_manager.py # 数据管理
├── ui/                 # 用户界面模块
│   ├── __init__.py
│   ├── main_window.py  # 主窗口
│   ├── control_panel.py # 控制面板
│   ├── action_list.py  # 操作列表
│   └── settings_dialog.py # 设置对话框
└── data/               # 数据存储目录
    └── (录制文件)
```

## 技术架构

### 核心模块
- **ActionRecorder**: 使用pynput库监听鼠标键盘事件
- **ActionPlayer**: 控制鼠标键盘执行录制的操作
- **DataManager**: 管理录制文件的保存、加载和导入导出

### UI框架
- **PyQt5**: 提供现代化的桌面应用程序界面
- **响应式设计**: 支持窗口缩放和高DPI显示
- **主题支持**: 可扩展的样式系统

## 开发说明

### 添加新功能
1. 在相应的核心模块中实现功能逻辑
2. 在UI模块中添加用户界面
3. 在主窗口中连接信号和槽
4. 更新设置对话框（如需要）

### 调试技巧
- 启用日志记录查看详细信息
- 使用设置中的调试选项
- 检查控制台输出的错误信息

## 常见问题

### Q: 录制不工作怎么办？
A: 请检查系统权限设置，确保应用程序有辅助功能权限。

### Q: 播放速度太快或太慢？
A: 在控制面板中调节播放速度，或在设置中修改默认播放速度。

### Q: 如何录制特定区域的操作？
A: 目前版本录制全屏操作，后续版本将支持区域录制功能。

### Q: 支持哪些文件格式？
A: 目前支持JSON格式，包含完整的操作数据和元信息。

## 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看LICENSE文件了解详情。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基本的录制和播放功能
- 完整的用户界面
- 文件管理功能
- 设置和配置选项

---

**注意**: 请负责任地使用此工具，遵守相关法律法规和使用条款。