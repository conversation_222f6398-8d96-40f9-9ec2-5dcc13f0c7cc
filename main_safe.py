#!/usr/bin/env python3
"""
操作录制器 - 安全模式启动
避免在macOS上的权限检查导致的崩溃问题
"""

import sys
import os

# 设置环境变量来抑制Qt警告（必须在导入PyQt5之前）
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.*.debug=false'
os.environ['QT_ASSUME_STDERR_HAS_CONSOLE'] = '1'

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# 尝试导入qRegisterMetaType，如果失败则忽略
try:
    from PyQt5.QtCore import qRegisterMetaType
except ImportError:
    # 某些PyQt5版本可能没有这个函数，定义一个空函数
    def qRegisterMetaType(type_name):
        pass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 不再使用复杂的Qt修复工具，保持简单


def check_dependencies():
    """检查依赖库是否安装"""
    missing_deps = []
    
    try:
        import PyQt5
        _ = PyQt5  # 避免未使用警告
    except ImportError:
        missing_deps.append("PyQt5")
    
    try:
        import pynput
        _ = pynput  # 避免未使用警告
    except ImportError:
        missing_deps.append("pynput")
    
    if missing_deps:
        print("缺少以下依赖库:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def show_permission_info():
    """显示权限信息"""
    import platform
    
    if platform.system() == "Darwin":  # macOS
        print("\n🔒 macOS 权限设置说明:")
        print("=" * 50)
        print("如果录制功能不工作，请按以下步骤设置权限:")
        print()
        print("1. 打开 系统偏好设置")
        print("2. 选择 安全性与隐私 > 隐私")
        print("3. 在左侧选择 '辅助功能'")
        print("4. 点击锁图标并输入密码")
        print("5. 点击 '+' 添加以下应用:")
        
        python_path = sys.executable
        print(f"   • Python解释器: {python_path}")
        print("   • 终端应用: /Applications/Utilities/Terminal.app")
        print("   • 或您使用的其他终端应用")
        
        print("6. 确保添加的应用都被勾选")
        print("7. 重新启动应用程序")
        print()
        print("💡 提示: 您也可以尝试用系统Python运行:")
        print("   /usr/bin/python3 main_safe.py")


def main():
    """主函数 - 安全模式"""
    print("操作录制器 v1.0 (安全模式)")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 显示权限信息
    show_permission_info()

    # 设置Qt属性（必须在创建QApplication之前）
    try:
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        print("✓ 高DPI支持已启用")
    except Exception as e:
        print(f"⚠️ 高DPI设置失败: {e}")

    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("操作录制器")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("AI Assistant")

    # 注册Qt元类型以避免警告（可选）
    try:
        qRegisterMetaType("QVector<int>")
        qRegisterMetaType("QList<int>")
        print("✓ Qt元类型注册成功")
    except Exception:
        # 如果注册失败，忽略错误（某些PyQt5版本可能不支持）
        print("⚠️ Qt元类型注册跳过（版本不支持）")
    
    try:
        # 导入主窗口（延迟导入避免早期崩溃）
        from ui.main_window import MainWindow
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 显示启动成功消息
        QMessageBox.information(
            main_window,
            "启动成功",
            "应用程序启动成功！\n\n"
            "新功能:\n"
            "• 📂 打开录制 - 在控制面板中点击打开按钮\n"
            "• 💾 保存录制 - 在控制面板中点击保存按钮\n"
            "• 📋 最近文件 - 文件菜单中的最近打开选项\n\n"
            "使用方法:\n"
            "• 点击控制面板的'📂 打开录制'按钮\n"
            "• 或使用菜单: 文件 > 打开录制 (Ctrl+O)\n"
            "• 或使用菜单: 文件 > 最近打开\n\n"
            "快捷键:\n"
            "• F9 - 开始/停止录制\n"
            "• F10 - 开始/停止播放\n"
            "• F11 - 暂停/恢复"
        )
        
        main_window.show()
        
        print("\n✅ 应用程序启动成功!")
        print("📱 GUI界面已打开")
        print("🎯 现在可以使用打开文件功能了!")
        print("   • 点击控制面板的'📂 打开录制'按钮")
        print("   • 或使用菜单: 文件 > 打开录制")
        print("   • 或使用菜单: 文件 > 最近打开")
        
        # 运行应用程序
        return app.exec_()
        
    except Exception as e:
        error_msg = f"应用程序启动失败:\n{str(e)}"
        print(f"❌ {error_msg}")
        
        # 尝试显示错误对话框
        try:
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
            
        return 1


if __name__ == "__main__":
    sys.exit(main())
